#!/bin/bash

echo "🌐 云归 Cloudflare R2 配置工具"
echo "=============================="

cd /www/wwwroot/ai.guiyunai.fun

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo ""
echo -e "${BLUE}请按照以下步骤配置Cloudflare R2：${NC}"
echo ""
echo "1. 访问 https://dash.cloudflare.com/"
echo "2. 进入 R2 Object Storage"
echo "3. 创建存储桶：guiyun-images-2025"
echo "4. 获取API凭证"
echo ""

# 获取用户输入
echo -e "${YELLOW}请输入您的Cloudflare配置信息：${NC}"
echo ""

read -p "Account ID: " ACCOUNT_ID
read -p "Access Key ID: " ACCESS_KEY
read -p "Secret Access Key: " SECRET_KEY
read -p "存储桶名称 [guiyun-images-2025]: " BUCKET_NAME
read -p "自定义域名 (可选，如 images.guiyunai.fun): " CUSTOM_DOMAIN

# 设置默认值
BUCKET_NAME=${BUCKET_NAME:-guiyun-images-2025}

# 确定存储桶URL
if [ -n "$CUSTOM_DOMAIN" ]; then
    BUCKET_URL="https://$CUSTOM_DOMAIN"
else
    echo ""
    echo -e "${YELLOW}请提供您的存储桶默认URL：${NC}"
    echo "格式类似：https://pub-xxxxxxxx.r2.dev"
    read -p "存储桶URL: " BUCKET_URL
fi

echo ""
echo -e "${BLUE}配置信息确认：${NC}"
echo "Account ID: ${ACCOUNT_ID:0:8}..."
echo "Access Key: ${ACCESS_KEY:0:8}..."
echo "Secret Key: ${SECRET_KEY:0:8}..."
echo "存储桶名称: $BUCKET_NAME"
echo "存储桶URL: $BUCKET_URL"
echo ""

read -p "确认配置正确？(y/n): " CONFIRM

if [ "$CONFIRM" != "y" ]; then
    echo "配置已取消"
    exit 1
fi

echo ""
echo -e "${BLUE}第一步：备份当前配置${NC}"
echo "======================"

# 创建备份
BACKUP_DIR="r2-config-backup-$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"
cp docker-compose.prod.yml "$BACKUP_DIR/"
echo "✅ 配置已备份到: $BACKUP_DIR"

echo ""
echo -e "${BLUE}第二步：更新Docker配置${NC}"
echo "======================"

# 更新docker-compose.prod.yml
sed -i "s/STORAGE_PROVIDER: \"local\"/STORAGE_PROVIDER: \"cloudflare\"/" docker-compose.prod.yml

# 添加Cloudflare配置
if ! grep -q "CLOUDFLARE_ACCOUNT_ID" docker-compose.prod.yml; then
    sed -i "/STORAGE_PROVIDER: \"cloudflare\"/a\\
      \\
      # Cloudflare R2 Configuration\\
      CLOUDFLARE_ACCOUNT_ID: \"$ACCOUNT_ID\"\\
      CLOUDFLARE_ACCESS_KEY: \"$ACCESS_KEY\"\\
      CLOUDFLARE_SECRET_ACCESS_KEY: \"$SECRET_KEY\"\\
      CLOUDFLARE_REGION: \"auto\"\\
      CLOUDFLARE_BUCKETNAME: \"$BUCKET_NAME\"\\
      CLOUDFLARE_BUCKET_URL: \"$BUCKET_URL\"" docker-compose.prod.yml
else
    # 更新现有配置
    sed -i "s/CLOUDFLARE_ACCOUNT_ID: \".*\"/CLOUDFLARE_ACCOUNT_ID: \"$ACCOUNT_ID\"/" docker-compose.prod.yml
    sed -i "s/CLOUDFLARE_ACCESS_KEY: \".*\"/CLOUDFLARE_ACCESS_KEY: \"$ACCESS_KEY\"/" docker-compose.prod.yml
    sed -i "s/CLOUDFLARE_SECRET_ACCESS_KEY: \".*\"/CLOUDFLARE_SECRET_ACCESS_KEY: \"$SECRET_KEY\"/" docker-compose.prod.yml
    sed -i "s/CLOUDFLARE_BUCKETNAME: \".*\"/CLOUDFLARE_BUCKETNAME: \"$BUCKET_NAME\"/" docker-compose.prod.yml
    sed -i "s|CLOUDFLARE_BUCKET_URL: \".*\"|CLOUDFLARE_BUCKET_URL: \"$BUCKET_URL\"|" docker-compose.prod.yml
fi

echo "✅ Docker配置已更新"

echo ""
echo -e "${BLUE}第三步：测试R2连接${NC}"
echo "=================="

echo "🧪 测试Cloudflare R2连接..."

# 创建测试脚本
cat > /tmp/test_r2.js << 'EOF'
const { S3Client, ListBucketsCommand } = require('@aws-sdk/client-s3');

const client = new S3Client({
  region: 'auto',
  endpoint: `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_ACCESS_KEY,
    secretAccessKey: process.env.CLOUDFLARE_SECRET_ACCESS_KEY,
  },
});

async function testConnection() {
  try {
    const command = new ListBucketsCommand({});
    const response = await client.send(command);
    console.log('✅ R2连接成功');
    console.log('存储桶列表:', response.Buckets?.map(b => b.Name));
    return true;
  } catch (error) {
    console.log('❌ R2连接失败:', error.message);
    return false;
  }
}

testConnection();
EOF

# 设置环境变量并测试
export CLOUDFLARE_ACCOUNT_ID="$ACCOUNT_ID"
export CLOUDFLARE_ACCESS_KEY="$ACCESS_KEY"
export CLOUDFLARE_SECRET_ACCESS_KEY="$SECRET_KEY"

# 如果有Node.js，运行测试
if command -v node >/dev/null 2>&1; then
    if npm list @aws-sdk/client-s3 >/dev/null 2>&1; then
        node /tmp/test_r2.js
    else
        echo "⚠️ 缺少AWS SDK，跳过连接测试"
    fi
else
    echo "⚠️ 未找到Node.js，跳过连接测试"
fi

echo ""
echo -e "${BLUE}第四步：重启服务${NC}"
echo "=================="

echo "🔄 重启Docker服务..."
docker compose -f docker-compose.prod.yml down
sleep 5
docker compose -f docker-compose.prod.yml up -d

echo "⏳ 等待服务启动..."
sleep 30

echo ""
echo -e "${BLUE}第五步：验证配置${NC}"
echo "=================="

echo "🧪 测试网站访问..."
SITE_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun)
echo "网站状态码: $SITE_STATUS"

if [ "$SITE_STATUS" = "200" ]; then
    echo "✅ 网站访问正常"
else
    echo "❌ 网站访问异常"
fi

echo ""
echo -e "${GREEN}配置完成！${NC}"
echo "=============="

echo ""
echo "📋 配置摘要："
echo "✅ 存储提供商已切换到Cloudflare R2"
echo "✅ 存储桶: $BUCKET_NAME"
echo "✅ 访问URL: $BUCKET_URL"
echo "✅ 备份文件保存在: $BACKUP_DIR"

echo ""
echo "🧪 测试步骤："
echo "1. 访问 https://ai.guiyunai.fun/launches"
echo "2. 尝试上传图片"
echo "3. 检查图片是否正常显示"

echo ""
echo "🔍 如果遇到问题："
echo "1. 检查Cloudflare R2存储桶权限设置"
echo "2. 确认API令牌权限正确"
echo "3. 查看应用日志: docker compose -f docker-compose.prod.yml logs postiz"
echo "4. 恢复备份: cp $BACKUP_DIR/docker-compose.prod.yml ."

echo ""
echo "✅ Cloudflare R2配置完成！"

# 清理临时文件
rm -f /tmp/test_r2.js
