# 云归 Cloudflare R2 配置详细指南

## 📋 配置概览

根据您的截图，您已经在Cloudflare R2页面。以下是完整的配置步骤：

## 🚀 第一步：创建R2存储桶

### 1.1 创建存储桶
```
1. 在R2 Object Storage页面，点击右上角 "Create bucket" 按钮
2. 存储桶名称：guiyun-images-2025
3. 地区选择：Auto（推荐）或选择离您最近的地区
4. 点击 "Create bucket" 完成创建
```

### 1.2 配置存储桶权限
```
1. 进入刚创建的存储桶
2. 点击 "Settings" 标签
3. 在 "Public access" 部分，启用公共读取权限
4. 保存设置
```

## 🔑 第二步：获取API凭证

### 2.1 获取Account ID
```
1. 在Cloudflare仪表板右侧边栏找到 "Account ID"
2. 复制这个ID，格式类似：a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6
```

### 2.2 创建API令牌
```
1. 在R2页面，点击右上角 "Manage R2 API tokens"
2. 点击 "Create API token"
3. 令牌名称：guiyun-r2-token
4. 权限设置：
   - Account: Cloudflare R2:Edit
   - Zone Resources: Include - All zones
5. 点击 "Continue to summary"
6. 点击 "Create Token"
7. 复制显示的 Access Key ID 和 Secret Access Key
```

## 🌐 第三步：配置自定义域名（推荐）

### 3.1 添加自定义域名
```
1. 在存储桶设置中，找到 "Custom Domains" 部分
2. 点击 "Connect Domain"
3. 输入域名：images.guiyunai.fun
4. 按照提示添加DNS记录到您的域名管理面板
```

### 3.2 DNS配置
```
在您的域名DNS管理中添加：
类型：CNAME
名称：images
值：您的存储桶域名（Cloudflare会提供）
```

## ⚙️ 第四步：项目配置

### 4.1 需要的配置信息
```bash
# 请将以下信息替换为您的实际值：
CLOUDFLARE_ACCOUNT_ID="您的Account ID"
CLOUDFLARE_ACCESS_KEY="您的Access Key ID"
CLOUDFLARE_SECRET_ACCESS_KEY="您的Secret Access Key"
CLOUDFLARE_REGION="auto"
CLOUDFLARE_BUCKETNAME="guiyun-images-2025"
CLOUDFLARE_BUCKET_URL="https://images.guiyunai.fun"  # 或使用默认URL
```

### 4.2 默认URL格式
如果不使用自定义域名，URL格式为：
```
https://pub-[随机字符串].r2.dev
```

## 📝 配置检查清单

- [ ] 存储桶已创建：guiyun-images-2025
- [ ] 公共访问已启用
- [ ] Account ID已获取
- [ ] API令牌已创建
- [ ] Access Key ID已获取
- [ ] Secret Access Key已获取
- [ ] 自定义域名已配置（可选）
- [ ] DNS记录已添加（如使用自定义域名）

## 🔍 常见问题

### Q1: 找不到Account ID？
A: 在Cloudflare仪表板主页右侧边栏，或者在任何页面的右下角都能找到。

### Q2: API令牌创建失败？
A: 确保您的Cloudflare账户已启用R2服务，可能需要绑定支付方式。

### Q3: 自定义域名配置失败？
A: 检查DNS记录是否正确添加，通常需要等待几分钟生效。

## 📞 获取帮助

如果遇到问题，请提供：
1. 您的Account ID（前几位即可）
2. 存储桶名称
3. 具体的错误信息

完成以上步骤后，请告诉我您获取到的配置信息，我将帮您更新项目配置。
